import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
from sklearn.metrics import mean_squared_error, r2_score
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Display options
pd.set_option("display.max_columns", None)
%matplotlib inline

# Load the dataset
try:
    df = pd.read_csv("data.csv")
    # Display first few rows
    print("First 5 rows of raw data:")
    try:
        from IPython.display import display
        display(df.head())
    except ImportError:
        print(df.head())
except FileNotFoundError:
    print("Error: data.csv not found. Please ensure the file is uploaded or in the correct directory.")
    # Exit if data not loaded
    exit()

# Shape of raw data
print(f"Dataset loaded successfully. Shape: {df.shape}")

# --- Check for Duplicates ---
print("\n--- Checking for Duplicate Rows ---")
df.duplicated().sum()


missing_values = df.isnull().sum()
missing_values = missing_values[missing_values > 0].sort_values(ascending=False)
print("\nFeatures with missing values:")
print(missing_values)

# Make copy to avoid modifying the original raw dataframe
# --- Column Name Cleaning ---
print("\n--- Cleaning Column Names ---")
df.columns.tolist()

df.columns = df.columns.str.lower().str.replace("[^\w]\s*", "", regex=True)
new_cols = df.columns.tolist()
new_cols

# Create a mapping for reference if needed, handling potential dulicates cautiously
col_mapping = {old: new for old, new in zip(df.columns, new_cols)}
print("Column names cleaned (lowercase, no spaces/special chars).")

df_new = df.copy()

df_new

# --- Missing Value Imputation ---
# Categorical features: Fill NaN with 'None' or 'NA' indicating absence
cat_cols_fill_none = [
    'Alley', 'BsmtQual', 'BsmtCond', 'BsmtExposure', 'BsmtFinType1',
    'BsmtFinType2', 'FireplaceQu', 'GarageType', 'GarageFinish',
    'GarageQual', 'GarageCond', 'PoolQC', 'Fence', 'MiscFeature',
    'MasVnrType'
]
for col in cat_cols_fill_none:
  if col in df.columns:
    df[col] = df[col].fillna('None')
cat_cols_fill_none

# Numerical features: Fill NaN appropriately
# LotFrontage: Impute with neighborhood median
if 'LotFrontage' in df.columns and 'Neighborhood' in df.columns:
    df['LotFrontage'] = df.groupby('Neighborhood')['LotFrontage'].transform(lambda x: x.fillna(x.median()))
    # Fill any remaining NaNs (if a neighborhood had all NaNs) with global median
    df['LotFrontage'] = df['LotFrontage'].fillna(df['LotFrontage'].median())
    print(f"LotFrontage imputed. Remaining NaNs: {df['LotFrontage'].isnull().sum()}")

# GarageYrBuilt: Impute with 0 for no garage, maybe YearBuilt otherwise? # GarageYrBlt: Impute with 0 for no garage, maybe YearBuilt otherwise? For simplicity, fill with 0 for now.
if 'GarageYrBlt' in df.columns:
    df['GarageYrBlt'] = df['GarageYrBlt'].fillna(0)

# MasVnrArea: Fill with 0
if 'MasVnrArea' in df.columns:
    df['MasVnrArea'] = df['MasVnrArea'].fillna(0)

# Electrical: Fill with mode (most frequent value)
if 'Electrical' in df.columns:
    df['Electrical'] = df['Electrical'].fillna(df['Electrical'].mode()[0])

# Basement numerical NaNs: Fill with 0
bsmt_num_cols_fill_zero = ['BsmtFinSF1', 'BsmtFinSF2', 'BsmtUnfSF', 'TotalBsmtSF', 'BsmtFullBath', 'BsmtHalfBath']
for col in bsmt_num_cols_fill_zero:
    if col in df.columns:
        df[col] = df[col].fillna(0)
bsmt_num_cols_fill_zero

# Garage numerical NaNs: Fill with 0
garage_num_cols_fill_zero = ['GarageCars', 'GarageArea']
for col in garage_num_cols_fill_zero:
    if col in df.columns:
        df[col] = df[col].fillna(0)
garage_num_cols_fill_zero

# Check remaining NaNs
print(f"Total remaining NaNs after imputation: {df.isnull().sum().sum()}")

# Check for specific column
print(df.isnull().sum()[df.isnull().sum() > 0])

# print("--- Engineering Features ---")
# Total Square Footage
# Use the cleaned column names (lowercase)
df['TotalSF'] = df['totalbsmtsf'] + df['1stflrsf'] + df['2ndflrsf']

# Total Bathrooms
# Use the cleaned column names (lowercase)
df['TotalBathrooms'] = df['fullbath'] + (0.5 * df['halfbath']) + df['bsmtfullbath'] + (0.5 * df['bsmthalfbath'])

# Total Porch Square Footage
# Use the cleaned column names (lowercase)
porch_cols = ['openporchsf', 'enclosedporch', '3ssnporch', 'screenporch', 'wooddecksf']
df['TotalPorchSF'] = df[porch_cols].sum(axis=1)
porch_cols

# Age Features
# Use the cleaned column names (lowercase)
df['HouseAge'] = df['yrsold'] - df['yearbuilt']
df['HouseAge'] = df['HouseAge'].apply(lambda x: max(0, x)) # No negative age
df['YearsSinceRemodel'] = df['yrsold'] - df['yearremodadd']
df['YearsSinceRemodel'] = df['YearsSinceRemodel'].apply(lambda x: max(0, x)) # No negative age
df['GarageAge'] = df.apply(lambda row: row['yrsold'] - row['garageyrblt'] if row['garageyrblt'] > 0 else 0, axis=1)
df['GarageAge'] = df['GarageAge'].apply(lambda x: max(0, x)) # No negative age

# Binary Indicators
df['IsNew'] = (df['HouseAge'] <= 1).astype(int)
# Use the cleaned, lowercase column names
df['HasPool'] = (df['poolarea'] > 0).astype(int)
df['HasGarage'] = (df['garagearea'] > 0).astype(int)
df['HasFireplace'] = (df['fireplaces'] > 0).astype(int)
df['HasBasement'] = (df['totalbsmtsf'] > 0).astype(int)
df['Has2ndFloor'] = (df['2ndflrsf'] > 0).astype(int)
# Use the correct column name 'TotalPorchSF' as created in the previous cell
df['HasPorch'] = (df['TotalPorchSF'] > 0).astype(int)
df['HasMasVnr'] = (df['masvnrarea'] > 0).astype(int)
# HasShed (from MiscFeature)
if 'miscfeature' in df.columns:
    df['hasshed'] = (df['miscfeature'] == 'Shed').astype(int)
    print("Created 'hasshed' binary indicator.")
else:
    df['hasshed'] = 0 # Assume no shed if column missing

# Interaction Features
# Use the cleaned, lowercase column names
df['QualxArea'] = df['overallqual'] * df['TotalSF']
df['QualxAge'] = df['overallqual'] * df['HouseAge']

# Polynomial Features (Squared Terms)
features_to_square = ['OverallQual', 'GrLivArea', 'TotalSF', 'HouseAge']
for feature in features_to_square:
    if feature in df.columns:
        df[f'{feature}_sq'] = df[feature] ** 2
features_to_square

# Neighborhood Price Encoding (Mean Target Encoding - potential for leakage if not done carefully, e.g., within CV)
# For simplicity here, we calculate it on the whole dataset before splitting.
# A more robust approach uses training fold data only or applies smoothing.
# Use the cleaned, lowercase column names 'neighborhood' and 'saleprice'
neighborhood_price_map = df.groupby('neighborhood')['saleprice'].mean()
df['NeighborhoodPrice'] = df['neighborhood'].map(neighborhood_price_map)

print("Feature engineering complete.")
neighborhood_price_map

# Exterior Material Consolidation
if 'exterior1st' in df.columns and 'exterior2nd' in df.columns:
    # Map based on Exterior1st, handle potential NaNs from mapping if needed
    exterior_map = {

        'VinylSd': 'Vinyl', 'MetalSd': 'Metal', 'Wd Sdng': 'Wood', 'HdBoard': 'Wood',

        'BrkFace': 'Brick', 'WdShing': 'Wood', 'CemntBd': 'Cement', 'Plywood': 'Wood',

        'AsbShng': 'Asbestos', 'Stucco': 'Stucco', 'BrkComm': 'Brick', 'AsphShn': 'Asphalt',

        'Stone': 'Stone', 'ImStucc': 'Stucco', 'CBlock': 'CBlock'

        # Add other mappings if necessary based on unique values

    }
    df['exterior_material'] = df['exterior1st'].map(exterior_map).fillna('Other')
    print("Consolidated exterior materials into 'exterior_material'.")
else:
    print("Warning: Exterior1st or Exterior2nd columns not found for consolidation.")
exterior_map

# LandSlope Simplification
if 'landslope' in df.columns:
    df['landslope'] = df['landslope'].map({'Gtl': 'flat', 'Mod': 'sloped', 'Sev': 'sloped'}).fillna('flat')
    print("Simplified 'landslope' into flat/sloped.")
df['landslope']

# Description of LandSlope Simplification
df['landslope'].describe()

# --- Ordinal Feature Encoding ---
# Define mappings (adjust based on actual values and desired order)
qual_cond_map = {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0}
bsmt_exp_map = {'Gd': 4, 'Av': 3, 'Mn': 2, 'No': 1, 'None': 0}
bsmt_fin_map = {'GLQ': 6, 'ALQ': 5, 'BLQ': 4, 'Rec': 3, 'LwQ': 2, 'Unf': 1, 'None': 0}
garage_fin_map = {'Fin': 3, 'RFn': 2, 'Unf': 1, 'None': 0}
fence_map = {'GdPrv': 4, 'MnPrv': 3, 'GdWo': 2, 'MnWw': 1, 'None': 0}
lot_shape_map = {'Reg': 0, 'IR1': 1, 'IR2': 2, 'IR3': 3} # Lower is more regular
land_slope_map = {'Gtl': 0, 'Mod': 1, 'Sev': 2} # Lower is gentler slope

print("--- Encoding Ordinal Features ---")
ordinal_cols_maps = {
    'ExterQual': qual_cond_map, 'ExterCond': qual_cond_map, 'BsmtQual': qual_cond_map,
    'BsmtCond': qual_cond_map, 'HeatingQC': qual_cond_map, 'KitchenQual': qual_cond_map,
    'FireplaceQu': qual_cond_map, 'GarageQual': qual_cond_map, 'GarageCond': qual_cond_map,
    'PoolQC': qual_cond_map, 'BsmtExposure': bsmt_exp_map, 'BsmtFinType1': bsmt_fin_map,
    'BsmtFinType2': bsmt_fin_map, 'GarageFinish': garage_fin_map, 'Fence': fence_map,
    'LotShape': lot_shape_map, 'LandSlope': land_slope_map
}

for col, mapping in ordinal_cols_maps.items():
    if col in df.columns:
        df[col] = df[col].map(mapping).fillna(0) # Fill potential new NaNs introduced by mapping with 0

print("Ordinal encoding complete.")
ordinal_cols_maps

# --- Numerical to Categorical Conversion ---
if 'MSSubClass' in df.columns:
    df['MSSubClass'] = df['MSSubClass'].astype(str)
    print("MSSubClass converted to string.")

# --- Target Transformation ---
# Log transform SalePrice to handle skewness
if 'SalePrice' in df.columns:
    df['SalePrice'] = np.log1p(df['SalePrice'])
    print("SalePrice log-transformed (log1p).")

# --- Outlier Handling (Example) ---
# IMPORTANT: Outlier removal is typically done *only* on the training set after splitting
# to avoid data leakage. Applying it here to the whole dataset before splitting is for demonstration.
# Consider implementing this within a cross-validation loop or after train_test_split in a real scenario.
print("\n--- Handling Outliers (Example - Apply carefully) ---")
initial_rows = len(df)
if 'grlivarea' in df.columns:
    df = df[df['grlivarea'] <= 4500]
if 'lotfrontage' in df.columns:
    df = df[df['lotfrontage'] <= 300]
if 'lotarea' in df.columns:
    df = df[df['lotarea'] <= 100000]
rows_removed = initial_rows - len(df)
print(f"Removed {rows_removed} rows based on example outlier thresholds (GrLivArea, LotFrontage, LotArea).")

# --- Drop Unnecessary Columns ---
print("--- Dropping Columns ---")
# Drop original columns used in aggregations/transformations if no longer needed
# Also drop columns identified as low variance, redundant, or problematic in previous analyses
cols_to_drop = [
    'Id', 'PID', # Identifiers
    'YearBuilt', 'YearRemodAdd', 'GarageYrBlt', 'YrSold', # Used in age features
    '1stFlrSF', '2ndFlrSF', 'TotalBsmtSF', # Used in TotalSF
    'FullBath', 'HalfBath', 'BsmtFullBath', 'BsmtHalfBath', # Used in TotalBathrooms
    'OpenPorchSF', 'EnclosedPorch', '3SsnPorch', 'ScreenPorch', 'WoodDeckSF', # Used in TotalPorchSF
    'PoolArea', 'Fireplaces', # Used in binary indicators
    'Alley', 'Street', 'Utilities', 'Condition2', 'RoofMatl', 'Heating', # Low variance/problematic
    'LowQualFinSF', 'MiscFeature', 'MiscVal' # Low variance/problematic
    'exterior1st', 'exterior2nd', # Replaced by exterior_material
    'miscfeature', # Replaced by hasshed
]
cols_to_drop

# Add columns that might exist and should be dropped
potential_drops = ['GarageCars'] # Often collinear with GarageArea
cols_to_drop.extend([col for col in potential_drops if col in df.columns])

# Drop columns that exist in the dataframe
existing_cols_to_drop = [col for col in cols_to_drop if col in df.columns]
df = df.drop(columns=existing_cols_to_drop)
print(f"Dropped {len(existing_cols_to_drop)} columns.")

print(f"Preprocessing and feature engineering finished. Final shape: {df.shape}")

# --- Feature Transformation (Skewness) ---
print("\n--- Handling Skewed Features ---")
# Identify numerical features (excluding the target if present)
numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
if "saleprice" in numerical_cols: # Exclude target
    numerical_cols.remove("saleprice")

# Calculate skewness
skewness = df[numerical_cols].apply(lambda x: x.skew()).sort_values(ascending=False)
print("Skewness of numerical features (Top 10):")
print(skewness.head(10))

# Apply log transformation (Log1p) to highly skewed features
# Threshold can be adjusted, e.g., 0.75 or 1
skew_threshold = 0.75
highly_skewed_cols = skewness[abs(skewness) > skew_threshold].index.tolist()

print(f"\nApplying log1p transformation to {len(highly_skewed_cols)} features with skewness > {skew_threshold}:\n")
highly_skewed_cols # Uncomment to see list

for col in highly_skewed_cols:
    # Ensure non-negative values before log transform if necessary
    if df[col].min() < 0:
        print(f"  Warning: Column {col} has negative values, skipping log transform.")
        continue
    df[col] = np.log1p(df[col])

print("Log transformation applied to skewed features.")
print("") # Add newline for spacing

# --- Outlier Handling (IQR Method) ---
print("\n--- Handling Outliers using IQR ---")
# IMPORTANT: Like thresholding, IQR outlier handling is best done *after* train/test split \n
# on the training data only and then applied to the test data, or within CV. \n
# Applying it here before splitting is for demonstration.

# Select numerical columns again (might have changed after transforms)
numerical_cols_for_outliers = df.select_dtypes(include=np.number).columns.tolist()
if "saleprice" in numerical_cols_for_outliers:
    numerical_cols_for_outliers.remove("saleprice")

outliers_imputed_count = 0
for col in numerical_cols_for_outliers:
    Q1 = df[col].quantile(0.25)
    Q3 = df[col].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    # Identify outliers
    outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
    if not outliers.empty:
        # Impute outliers with the median (or use winsorization)
        median_val = df[col].median()
        df.loc[outliers.index, col] = median_val
        outliers_imputed_count += len(outliers)
        # print(f"  Imputed {len(outliers)} outliers in {col} with median {median_val:.2f}") # Uncomment for detail

print(f"Total outliers imputed using IQR method across numerical features: {outliers_imputed_count}")
# Note: The previous threshold-based outlier removal is now redundant if IQR is used.
# Consider commenting out or removing the threshold-based removal cell added previously.
print("") # Add newline for spacing


# --- Correlation-Based Feature Dropping ---
print("\n--- Dropping Highly Correlated Features ---")
# Select numerical columns
numerical_cols_for_corr = df.select_dtypes(include=np.number).columns.tolist()
if "saleprice" in numerical_cols_for_corr:
    numerical_cols_for_corr.remove("saleprice")

# Calculate correlation matrix
corr_matrix = df[numerical_cols_for_corr].corr().abs()

# Select upper triangle of correlation matrix
upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))

# Find features with correlation greater than threshold (e.g., 0.9)
corr_threshold = 0.90
cols_to_drop_corr = [column for column in upper_tri.columns if any(upper_tri[column] > corr_threshold)]

if cols_to_drop_corr:
    print(f"Found {len(cols_to_drop_corr)} features to drop due to high correlation (> {corr_threshold}):")
    # print(cols_to_drop_corr) # Uncomment to see list
    df = df.drop(columns=cols_to_drop_corr)
    print(f"Dropped highly correlated features. New shape: {df.shape}")
else:
    print("No features dropped based on correlation threshold.")
print("") # Add newline for spacing


# Define the filename for the preprocessed data
preprocessed_file = 'house_price_preprocessed_data.csv'

# Save the preprocessed DataFrame to a CSV file
try:
    df.to_csv(preprocessed_file, index=False)
    print(f'Preprocessed data saved successfully to {preprocessed_file}')
except Exception as e:
    print(f'Error saving preprocessed data: {e}')

# Read the preprocessed data back from the CSV file
try:
    df_loaded = pd.read_csv(preprocessed_file)
    print(f'Preprocessed data reloaded successfully from {preprocessed_file}. Shape: {df_loaded.shape}')

    # Display the first few rows of the reloaded data
    print("\nFirst 5 rows of reloaded preprocessed data:")
    try:
        from IPython.display import display
        display(df_loaded.head())
    except ImportError:
        print(df_loaded.head())

    # Optional: Assign back to df if subsequent steps use 'df'
    df = df_loaded.copy()

except FileNotFoundError:
    print(f'Error: {preprocessed_file} not found. Please ensure the saving step was successful.')
    # Exit if data not loaded
    exit()
except Exception as e:
    print(f'Error reloading preprocessed data: {e}')
    # Exit if data not loaded
    exit()

from sklearn.model_selection import train_test_split # Import the function for splitting data



# Ensure the reloaded dataframe 'df' (containing fully preprocessed data) is used

# Separate features (X) and the target variable (y)

if "saleprice" in df.columns:

    # X contains all columns EXCEPT the target variable 'saleprice'

    X = df.drop("saleprice", axis=1)



    # y contains ONLY the target variable 'saleprice' (which is already log-transformed)

    y = df["saleprice"]



    print(f"Features (X) shape: {X.shape} - {X.shape[0]} rows, {X.shape[1]} features")

    print(f"Target (y) shape: {y.shape} - {y.shape[0]} values")

else:

    print("Error: 'saleprice' column not found in the reloaded dataframe.")

    # Handle error appropriately, maybe exit()

    X, y = None, None # Set to None to prevent further errors



if X is not None and y is not None:


    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)



    print("\nData successfully split into training and testing sets:")

    print(f"  X_train shape: {X_train.shape} - Training features")

    print(f"  X_test shape: {X_test.shape} - Testing features")

    print(f"  y_train shape: {y_train.shape} - Training target")

    print(f"  y_test shape: {y_test.shape} - Testing target")

else:

    print("\nSkipping train/test split due to missing target variable.")










