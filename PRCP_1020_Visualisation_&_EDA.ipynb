import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

# Set plot style
sns.set_style("whitegrid")

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Display options
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", 100) # Show more rows for missing value analysis
%matplotlib inline

try:
    df_raw = pd.read_csv("data.csv")
    print(f"Raw dataset loaded successfully. Shape: {df_raw.shape}")
except FileNotFoundError:
    print("Error: data.csv not found. Please ensure the file is in the correct directory.")
    df_raw = None # Set to None if loading fails

# First 5 rows
print("First 5 rows of raw data:")
display(df_raw.head())

# Data Info
print("Raw Data Info:")
df_raw.info()

# Data Numerical Summary
print("Raw Data Numerical Summary:")
display(df_raw.describe())

# Data Categorical Summary
print("Raw Data Categorical Summary:")
display(df_raw.describe(include="object"))

# Calculate missing values
df_raw.isnull().sum()

# Calculate missing values
missing_values_raw = df_raw.isnull().sum()

# Calculate and display percentage of missing values
missing_percent_raw = (missing_values_raw / len(df_raw)) * 100 # Assign the result to missing_percent_raw

missing_data_raw = pd.DataFrame({"Missing Count": missing_values_raw, "Missing Percent": missing_percent_raw})
# Filter to show only columns with missing values
missing_data_raw = missing_data_raw[missing_data_raw["Missing Count"] > 0].sort_values(by="Missing Percent", ascending=False)

print(f"Total columns with missing values in raw data: {len(missing_data_raw)}")
print("Features with missing values (Raw Data):")
display(missing_data_raw)

# Visualize missing values pattern
plt.figure(figsize=(15, 8))
sns.heatmap(df_raw.isnull(), cbar=False, cmap="viridis")
plt.title("Missing Value Pattern in Raw Data")
plt.show()

if df_raw is not None:
    print("--- Histograms for Key Numerical Features --- \n")
    key_num_cols_raw = ["SalePrice", "GrLivArea", "TotalBsmtSF", "LotArea", "YearBuilt"]
    for col in key_num_cols_raw:
        if col in df_raw.columns:
            plt.figure(figsize=(10, 5))
            sns.histplot(df_raw[col].dropna(), kde=True, bins=40) # dropna() for safety
            plt.title(f"Distribution of {col} (Raw)")
            plt.show()
            # Print skewness and kurtosis for SalePrice
            if col == "SalePrice":
                 print(f"Skewness (Raw {col}): {df_raw[col].skew()}")
                 print(f"Kurtosis (Raw {col}): {df_raw[col].kurt()}")

print("--- Univariate Box Plots for Key Numerical Features (Raw Data) --- \n")
key_num_cols_raw_box = ["SalePrice", "GrLivArea", "LotArea", "LotFrontage"] # Added LotFrontage
plt.figure(figsize=(10, 3))
sns.boxplot(x=df_raw[col].dropna())
plt.title(f"Box Plot of {col} (Raw)")
plt.show()

if df_raw is not None:
    print("--- Count Plots for Key Categorical Features ---")
    key_cat_cols_raw = ["OverallQual", "Neighborhood", "HouseStyle", "MSZoning", "CentralAir", "PavedDrive"]
    for col in key_cat_cols_raw:
        if col in df_raw.columns:
            plt.figure(figsize=(10, 5))
            # Use dropna() for countplot as it doesn't handle NaN well by default
            plot_data = df_raw[col].dropna()
            order = plot_data.value_counts().index
            sns.countplot(y=plot_data, order=order)
            plt.title(f"Counts for {col} (Raw)")
            plt.tight_layout()
            plt.show()

print("--- Count Plots for Key Categorical Features (Raw Data) ---")
key_cat_cols_raw = ["OverallQual", "Neighborhood", "HouseStyle", "MSZoning", "CentralAir", "PavedDrive"]
plt.figure(figsize=(10, 5))
# Use dropna() for countplot as it doesn't handle NaN well by default
plot_data = df_raw[col].dropna()
order = plot_data.value_counts().index
sns.countplot(y=plot_data, order=order)
plt.title(f"Counts for {col} (Raw)")
plt.tight_layout()
plt.show()

# Upgraded Python code for a multi-layered donut chart
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

def create_advanced_donut_chart(df, outer_col, middle_col, inner_col, title="Advanced Multi-Layer Donut Chart"):
    """
    Creates an advanced, multi-layered donut chart with improved clarity.

    Args:
        df (pd.DataFrame): The dataframe containing the data.
        outer_col (str): The column name for the outer ring.
        middle_col (str): The column name for the middle ring.
        inner_col (str): The column name for the inner ring.
        title (str): The title for the chart.
    """
    # --- Input Validation ---
    if not isinstance(df, pd.DataFrame):
        print("Error: Input 'df' must be a pandas DataFrame.")
        return

    required_cols = [outer_col, middle_col, inner_col]
    if not all(col in df.columns for col in required_cols):
        print(f"Error: One or more columns ({required_cols}) not found in DataFrame.")
        return

    # --- Data Preparation ---
    try:
        outer_data = df[outer_col].dropna().value_counts()
        middle_data = df[middle_col].dropna().value_counts()
        inner_data = df[inner_col].dropna().value_counts()

        if outer_data.empty or middle_data.empty or inner_data.empty:
            print("Error: One or more columns have no data after dropping NaNs.")
            return
    except Exception as e:
        print(f"Error during data preparation: {e}")
        return

    # --- Color Palettes ---
    # Using distinct palettes for each layer
    outer_colors = sns.color_palette("Set3", len(outer_data))
    middle_colors = sns.color_palette("Paired", len(middle_data))
    # Use a palette suitable for more categories for the inner ring
    inner_colors = sns.color_palette("tab10", len(inner_data))
    # If more than 10 inner categories, consider 'tab20' or generating custom colors
    if len(inner_data) > 10:
        inner_colors = sns.color_palette("tab20", len(inner_data))

    # --- Plotting Setup ---
    fig, ax = plt.subplots(figsize=(14, 14))
    ax.set_title(title, fontsize=20, fontweight="bold", pad=20)

    # --- Define Radii and Width ---
    outer_radius = 1.0
    middle_radius = 0.7
    inner_radius = 0.4
    wedge_width = 0.3

    # --- Plot Outer Ring (e.g., Street) ---
    wedges_outer, texts_outer = ax.pie(
        outer_data.values,
        radius=outer_radius,
        colors=outer_colors,
        labels=outer_data.index,
        startangle=90,
        counterclock=False,
        labeldistance=1.05, # Place labels just outside the ring
        textprops={"fontsize": 12, "fontweight": "bold", "color": "black"},
        wedgeprops=dict(width=wedge_width, edgecolor="white", linewidth=2)
    )
    # Improve outer label visibility
    for text in texts_outer:
        text.set_horizontalalignment("center")

    # --- Plot Middle Ring (e.g., CentralAir) ---
    wedges_middle, texts_middle = ax.pie(
        middle_data.values,
        radius=middle_radius,
        colors=middle_colors,
        labels=middle_data.index,
        startangle=90,
        counterclock=False,
        labeldistance=1.05, # Place labels just outside this ring (inside outer)
        textprops={"fontsize": 11, "fontweight": "bold", "color": "dimgray"},
        wedgeprops=dict(width=wedge_width, edgecolor="white", linewidth=2)
    )
    # Improve middle label visibility
    for text in texts_middle:
        text.set_horizontalalignment("center")

    # --- Plot Inner Ring (e.g., MSZoning) ---
    wedges_inner, texts_inner_labels, texts_inner_pcts = ax.pie(
        inner_data.values,
        radius=inner_radius,
        colors=inner_colors,
        labels=inner_data.index,
        autopct="%.1f%%", # Show percentages
        startangle=90,
        counterclock=False,
        pctdistance=0.8,  # Place percentages inside the ring
        labeldistance=1.1, # Place labels just outside this ring (inside middle)
        textprops={"fontsize": 10, "fontweight": "bold"},
        wedgeprops=dict(width=wedge_width, edgecolor="white", linewidth=2)
    )

    # Customize inner labels and percentages for clarity
    for label in texts_inner_labels:
        label.set_color("navy")
        label.set_horizontalalignment("center")
    for pct in texts_inner_pcts:
        pct.set_color("white")
        pct.set_fontsize(9)
        pct.set_fontweight("bold")

    # --- Add Center Circle & Text (Optional) ---
    center_circle = plt.Circle((0, 0), 0.1, fc="white") # Radius based on inner ring edge
    fig.gca().add_artist(center_circle)
    # Optional: Add text in the center
    # plt.text(0, 0, 'Housing\nFeatures', ha='center', va='center', fontsize=14, fontweight='bold')

    # --- Final Touches ---
    ax.axis("equal") # Equal aspect ratio ensures that pie is drawn as a circle.
    plt.tight_layout()
    plt.show()

# --- Example Usage ---
# First, ensure you have the DataFrame 'df_raw' loaded.
# If not, load it:
try:
    # Assuming data.csv is in the same directory
    df_raw = pd.read_csv("data.csv")
    print("Loaded data.csv for example usage.")

    # Define columns for the layers
    outer_column = "Street"
    middle_column = "CentralAir"
    inner_column = "MSZoning"

    # Create the chart
    create_advanced_donut_chart(df_raw, outer_column, middle_column, inner_column,
                                title=f"Housing Details: {outer_column}, {middle_column}, {inner_column}")

except FileNotFoundError:
    print("Error: data.csv not found. Cannot run example usage.")
    # Create a dummy DataFrame for testing if needed
    # data = {
    #     'Street': np.random.choice(['Pave', 'Grvl'], 100, p=[0.95, 0.05]),
    #     'CentralAir': np.random.choice(['Y', 'N'], 100, p=[0.9, 0.1]),
    #     'MSZoning': np.random.choice(['RL', 'RM', 'FV', 'RH', 'C (all)'], 100, p=[0.6, 0.2, 0.1, 0.05, 0.05])
    # }
    # df_raw = pd.DataFrame(data)
    # print("Created dummy data for testing.")
    # create_advanced_donut_chart(df_raw, 'Street', 'CentralAir', 'MSZoning',
    #                             title="Dummy Housing Details: Street, CentralAir, MSZoning")

except Exception as e:
    print(f"An error occurred during example usage: {e}")




if df_raw is not None:
    print("--- Scatter Plots (Numerical vs. SalePrice) ---")
    key_num_scatter_raw = ["GrLivArea", "TotalBsmtSF", "1stFlrSF", "GarageArea", "YearBuilt"]

    # Determine the number of rows and columns for the subplots
    n_cols = 3  # You can adjust the number of columns
    n_rows = (len(key_num_scatter_raw) + n_cols - 1) // n_cols # Calculate required rows

    # Get a colorful palette with enough colors
    colorful_palette = sns.color_palette("viridis", len(key_num_scatter_raw))

    # Create subplots
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols * 5, n_rows * 5))
    # Flatten the axes array for easy iteration if it's a 2D array
    axes = axes.flatten()

    for i, col in enumerate(key_num_scatter_raw):
        if col in df_raw.columns:
            # Select the correct subplot axis
            ax = axes[i]

            # Drop NA for the pair being plotted
            plot_data = df_raw.dropna(subset=[col, "SalePrice"])

            # Use the specific color from the palette for each plot
            sns.scatterplot(x=col, y="SalePrice", data=plot_data, alpha=0.6, color=colorful_palette[i], ax=ax)
            ax.set_title(f"{col} vs. SalePrice (Raw)")
            ax.set_xlabel(col)
            ax.set_ylabel("SalePrice")

    # Hide any unused subplots
    for j in range(i + 1, len(axes)):
        fig.delaxes(axes[j])

    plt.tight_layout() # Adjust layout to prevent overlapping titles/labels
    plt.show()

if df_raw is not None:
    print("--- Box Plots (Categorical vs. SalePrice - Raw Data) ---\n")
    key_cat_box_raw = ["OverallQual", "Neighborhood", "CentralAir", "GarageCars"]
    for col in key_cat_box_raw:
        if col in df_raw.columns:
            plt.figure(figsize=(12, 6))
            # Drop NA for the pair being plotted
            plot_data = df_raw.dropna(subset=[col, "SalePrice"])

            if plot_data.empty:
                print(f"Warning: No data available for plotting {col} vs SalePrice after dropping NaNs.")
                plt.close() # Close the empty figure
                continue
            # Sort categories for better visualization if possible (e.g., OverallQual)
            try:
                # Attempt to sort numerically if possible, else use value counts
                unique_vals = plot_data[col].unique()
                # Check if the unique values can be interpreted as numeric
                if pd.api.types.is_numeric_dtype(pd.Series(unique_vals)):
                     # Convert to numeric if possible for sorting
                     try:
                         order = sorted(pd.to_numeric(unique_vals))
                     except ValueError:
                          # If conversion fails, sort as strings
                          order = sorted(unique_vals)
                else:
                     # For non-numeric types, sort by frequency
                     order = plot_data[col].value_counts().index.tolist()
            except Exception:
                # Fallback for any other unexpected errors
                order = plot_data[col].value_counts().index.tolist() # Fallback to sorting by frequency
            # Get a palette suitable for the number of categories in the current column
            num_categories = len(plot_data[col].unique())

            if num_categories <= 10:
                 category_palette = sns.color_palette("tab10", num_categories)
            elif num_categories <= 20:
                 category_palette = sns.color_palette("tab20", num_categories)
            else:
                 category_palette = sns.color_palette("viridis", num_categories)
            sns.boxplot(x=col, y="SalePrice", data=plot_data, order=order, palette=category_palette)
            plt.title(f"{col} vs. SalePrice (Raw)")
            plt.xlabel(col)
            plt.ylabel("SalePrice")

            if plot_data[col].nunique() > 10: # Rotate labels if many categories
                 plt.xticks(rotation=60, ha="right")
            plt.tight_layout()
            plt.show()

if df_raw is not None:
    print("--- Violin Plots (Categorical vs. SalePrice) ---")
    key_cat_violin_raw = ["OverallQual", "CentralAir", "PavedDrive"] # Choose a few for demonstration

    for col in key_cat_violin_raw:
        if col in df_raw.columns:
            plt.figure(figsize=(12, 6))
            plot_data = df_raw.dropna(subset=[col, "SalePrice"])
            try:
                unique_vals = plot_data[col].unique()
                if pd.api.types.is_numeric_dtype(unique_vals):
                     order = sorted(unique_vals)
                else:
                     order = plot_data[col].value_counts().index
            except TypeError:
                order = plot_data[col].value_counts().index

            sns.violinplot(x=col, y="SalePrice", data=plot_data, order=order,palette='rocket', inner="quartile") # inner="quartile" shows quartiles
            plt.title(f"{col} vs. SalePrice (Raw - Violin Plot)")
            plt.tight_layout()
            plt.show()

if df_raw is not None:
    print("--- Bar Plots (Average SalePrice by Category) ---\n")
    key_cat_bar_raw = ["HouseStyle", "MSZoning", "Foundation"]
    for col in key_cat_bar_raw:
        if col in df_raw.columns:
            plt.figure(figsize=(10, 5))
            plot_data = df_raw.dropna(subset=[col, "SalePrice"])
            # Calculate mean SalePrice per category
            order = plot_data.groupby(col)["SalePrice"].mean().sort_values(ascending=False).index
            sns.barplot(x=col, y="SalePrice", data=plot_data, order=order,palette=category_palette, estimator=np.mean, errorbar=None) # Use errorbar=None instead of ci=None
            plt.title(f"Average SalePrice by {col} (Raw)")
            plt.xticks(rotation=45, ha="right")
            plt.tight_layout()
            plt.show()

# Joint plot for GrLivArea and SalePrice
col = "GrLivArea"
plot_data = df_raw.dropna(subset=[col, "SalePrice"])
g = sns.jointplot(
    x=col, y="SalePrice", data=plot_data, kind="scatter",
    color=sns.color_palette("rocket", 1)[0],  # Using a single color from palette
    marginal_kws={'color': sns.color_palette("viridis", 1)[0], 'edgecolor': 'black'},
    s=20, alpha=0.6, edgecolor='w'
)
g.fig.suptitle(f"Joint Distribution of {col} and SalePrice (Raw)", y=1.02)
plt.show()

# Joint plot for TotalBsmtSF and SalePrice
col = "TotalBsmtSF"
plot_data = df_raw.dropna(subset=[col, "SalePrice"])
g = sns.jointplot(
    x=col, y="SalePrice", data=plot_data, kind="scatter",
    color=sns.color_palette("viridis", 1)[0], # Using a single color from palette
    marginal_kws={'color': sns.color_palette("viridis", 1)[0], 'edgecolor': 'black'},
    s=20, alpha=0.6, edgecolor='w'
)
g.fig.suptitle(f"Joint Distribution of {col} and SalePrice (Raw)", y=1.02)
plt.show()

print("--- Correlation Heatmap (Raw Data) ---")

# Select only numerical columns for correlation
numerical_cols_raw = df_raw.select_dtypes(include=np.number).columns
correlation_matrix_raw = df_raw[numerical_cols_raw].corr()
plt.figure(figsize=(18, 14))
sns.heatmap(correlation_matrix_raw, cmap="coolwarm", annot=False, fmt=".1f") # annot=True is too crowded
plt.title("Correlation Matrix of Numerical Features (Raw Data)")
plt.show()

# Show top correlations with SalePrice
print("Top Absolute Correlations with SalePrice (Raw):")
if "SalePrice" in correlation_matrix_raw.columns:
    top_corr_raw = correlation_matrix_raw["SalePrice"].abs().sort_values(ascending=False)
    display(top_corr_raw.head(15))
else:
    print("SalePrice column not found for correlation analysis.")

print("--- Pair Plot (Subset of Key Features) ---")
# Select a small subset of key numerical features + target
key_features_pair_raw = ["SalePrice", "OverallQual", "GrLivArea", "GarageArea", "TotalBsmtSF"]
key_features_pair_raw = [col for col in key_features_pair_raw if col in df_raw.columns]
if len(key_features_pair_raw) > 1:
        # Drop rows with NA in this subset for the pairplot
        plot_data = df_raw[key_features_pair_raw].dropna()

        # Use a colorful palette for the scatter plots
        colorful_palette = sns.color_palette("viridis", len(key_num_scatter_raw))

        g = sns.pairplot(plot_data, diag_kind="kde", palette='colorful_palette') # Use kde for diagonal plots

        # Fix for overlapping SalePrice X axis labels
        for ax in g.axes.flatten():
            if ax.get_xlabel() == "SalePrice":
                 plt.setp(ax.get_xticklabels(), rotation=45, ha="right") # Rotate labels

        g.fig.suptitle("Pairwise Relationships of Key Features (Raw)", y=1.02)
        plt.show()
else:
  print("Not enough key features found for pair plot.")

print("--- FacetGrid Example (SalePrice Distribution by OverallQual) ---")
if "OverallQual" in df_raw.columns and "SalePrice" in df_raw.columns:
    plot_data = df_raw.dropna(subset=["OverallQual", "SalePrice"])

    # Ensure 'OverallQual' is treated as a category for consistent colors
    # You might want to convert it to a category type or string
    plot_data['OverallQual_str'] = plot_data['OverallQual'].astype(str)

    # Create the FacetGrid without specifying palette here
    g = sns.FacetGrid(plot_data, col="OverallQual_str", col_wrap=5) # Use the string version

    # Map the histplot function - colors will be set manually next
    g.map(sns.histplot, "SalePrice", kde=False, bins=10)

    # Apply colors manually to each subplot using a palette
    colors = sns.color_palette('viridis', n_colors=len(plot_data['OverallQual_str'].unique()))

    for ax, color in zip(g.axes.flat, colors):
        # Find the artists (like the histogram bars) and set their color
        for patch in ax.patches: # Access the histogram bars
            patch.set_facecolor(color)

    g.fig.suptitle("SalePrice Distribution Faceted by OverallQual", y=1.03)
    g.set_titles("Qual {col_name}")
    plt.tight_layout()
    plt.show()
else:
    print("OverallQual or SalePrice not found for FacetGrid.")

print("--- Subplots Example (Multiple Numerical Distributions) ---")
key_num_subplot_raw = ["LotArea", "TotalBsmtSF", "1stFlrSF", "GrLivArea"]
key_num_subplot_raw = [col for col in key_num_subplot_raw if col in df_raw.columns]

if len(key_num_subplot_raw) == 4:
        fig, axes = plt.subplots(2, 2, figsize=(12, 10)) # Create a 2x2 grid
        fig.suptitle("Distributions using Subplots", fontsize=16)

        sns.histplot(df_raw[key_num_subplot_raw[0]].dropna(),color='ORANGE', ax=axes[0, 0], kde=True)
        axes[0, 0].set_title(key_num_subplot_raw[0])

        sns.histplot(df_raw[key_num_subplot_raw[1]].dropna(),color='ORANGE', ax=axes[0, 1], kde=True)
        axes[0, 1].set_title(key_num_subplot_raw[1])

        sns.histplot(df_raw[key_num_subplot_raw[2]].dropna(),color='ORANGE', ax=axes[1, 0], kde=True)
        axes[1, 0].set_title(key_num_subplot_raw[2])

        sns.histplot(df_raw[key_num_subplot_raw[3]].dropna(),color='ORANGE', ax=axes[1, 1], kde=True)
        axes[1, 1].set_title(key_num_subplot_raw[3])

        plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout
        plt.show()
else:
  print("Could not find all 4 specified columns for subplot example.")


try:
    df_processed = pd.read_csv("house_price_preprocessed_data.csv")
    print(f"Preprocessed dataset loaded successfully. Shape: {df_processed.shape}")
except FileNotFoundError:
    print("Error: house_price_preprocessed_data.csv not found. Please ensure the file is in the correct directory.")
    df_processed = None # Set to None if loading fails

# First 5 rows
print("First 5 rows of preprocessed data:")
display(df_processed.head())

# Processed Data Info
print("\Preprocessed Data Info:")
df_processed.info()

 # Check for missing values (should be none or very few)
print(f"Total missing values in preprocessed data: {df_processed.isnull().sum().sum()}")

# Numerical Summary of Processed Data
print("Preprocessed Data Numerical Summary:")
display(df_processed.describe())

# Preprocessed Data Categorical Summary
print("Preprocessed Data Categorical Summary:")
display(df_processed.describe(include="object"))

print("--- Histograms for Key Numerical Features (Preprocessed Data) ---")
# Identify target column (could be SalePrice or a transformed version)
target_col_proc = None
if "saleprice_log" in df_processed.columns.str.lower():
    target_col_proc = [col for col in df_processed.columns if col.lower() == "saleprice_log"][0]
elif "SalePrice" in df_processed.columns:
    target_col_proc = "SalePrice"
elif "saleprice" in df_processed.columns.str.lower(): # Check lowercase just in case
    target_col_proc = [col for col in df_processed.columns if col.lower() == "saleprice"][0]
key_num_cols_proc = [target_col_proc, "grlivarea", "totalbsmtsf", "lotarea", "yearbuilt"]
# Add potential engineered features
engineered_features_check = ["totalsf", "houseage"]
key_num_cols_proc.extend([col for col in engineered_features_check if col in df_processed.columns.str.lower()])
key_num_cols_proc = [col for col in key_num_cols_proc if col is not None and col in df_processed.columns]
key_num_cols_proc = list(dict.fromkeys(key_num_cols_proc)) # Remove duplicate
for col in key_num_cols_proc:
        plt.figure(figsize=(10, 5))
        sns.histplot(df_processed[col].dropna(), kde=True, bins=40)
        plt.title(f"Distribution of {col} (Preprocessed)")
        plt.show()
        if col == target_col_proc:
            print(f"Skewness ({col}): {df_processed[col].skew()}")
            print(f"Kurtosis ({col}): {df_processed[col].kurt()}")

print("--- Univariate Box Plots for Key Numerical Features (Preprocessed Data) ---\n")
key_num_cols_proc_box = [target_col_proc, "grlivarea", "lotarea", "lotfrontage"]
key_num_cols_proc_box = [col for col in key_num_cols_proc_box if col is not None and col in df_processed.columns]

# Determine the number of columns to plot
n_cols = len(key_num_cols_proc_box)
if n_cols > 0:
    # Create subplots: one row, multiple columns (equal to the number of features)
    fig, axes = plt.subplots(1, n_cols, figsize=(n_cols * 3, 8)) # Adjust figsize as needed
    # Get a colorful palette with enough colors
    colorful_palette = sns.color_palette("viridis", n_cols)
    # Ensure 'axes' is an iterable even if there's only one column
    if n_cols == 1:
        axes = [axes]
    for i, col in enumerate(key_num_cols_proc_box):
        # Select the correct subplot axis
        ax = axes[i]
        # Use y-axis for the boxplot to make it vertical
        sns.boxplot(y=df_processed[col].dropna(), color=colorful_palette[i], ax=ax) # Assign color and specify axis
        ax.set_title(f"{col} (Preprocessed)")
        ax.set_ylabel(col) # Set y-axis label
    plt.tight_layout() # Adjust layout to prevent overlapping
    plt.show()
else:
    print("No valid numerical columns found for plotting.")

# Count Plots for Key Categorical Features (Preprocessed Data)
print("--- Count Plots for Key Categorical Features (Preprocessed Data) --- \n")
# Find categorical columns (excluding potentially high-cardinality engineered ones if needed)
categorical_cols_proc = df_processed.select_dtypes(include="object").columns.tolist()
# Use lowercase names if they exist in the processed data
key_cat_cols_proc = ["overallqual", "neighborhood", "housestyle", "mszoning", "centralair", "paveddrive"]
key_cat_cols_proc = [col for col in key_cat_cols_proc if col in df_processed.columns and (df_processed[col].dtype == "object" or df_processed[col].dtype == "int64")] # Include int like OverallQual
for col in key_cat_cols_proc:
    plt.figure(figsize=(12, 6)) # Adjusted figsize
    plot_data = df_processed[col].dropna()
    order = plot_data.value_counts().index
    # Use a colorful palette based on the number of unique categories
    num_categories = len(order)
    if num_categories <= 10:
        category_palette = sns.color_palette("tab10", num_categories)
    elif num_categories <= 20:
        category_palette = sns.color_palette("tab20", num_categories)
    else:
        # For more categories, a larger palette or a sequential one might be better
        category_palette = sns.color_palette("viridis", num_categories)
    sns.countplot(y=plot_data, order=order, palette=category_palette) # Use the colorful palette
    plt.title(f"Counts for {col} (Preprocessed)", fontsize=14) # Increased title font size
    plt.xlabel("Count", fontsize=12) # Added axis labels with font size
    plt.ylabel(col.capitalize(), fontsize=12)
    plt.tight_layout()
    plt.show()

def create_donut_chart_processed(df, column, title="Donut Chart"):
    # --- Input Validation ---
    if not isinstance(df, pd.DataFrame):
        print("Error: Input 'df' must be a pandas DataFrame.")
        return
    if column not in df.columns:
        print(f"Error: Column '{column}' not found in DataFrame.")
        return
    # --- Data Preparation ---
    try:
        data_counts = df[column].dropna().value_counts()
        if data_counts.empty:
            print(f"Error: Column '{column}' has no data after dropping NaNs.")
            return
        # Calculate percentages
        data_percentages = data_counts / data_counts.sum() * 100
        # Combine counts and percentages for labels
        labels = [f'{idx} ({count} - {pct:.1f}%)' for idx, count, pct in zip(data_counts.index, data_counts.values, data_percentages.values)]
    except Exception as e:
        print(f"Error during data preparation for column '{column}': {e}")
        return

    # --- Color Palette ---
    # Use a colorful palette based on the number of categories
    num_categories = len(data_counts)
    if num_categories <= 10:
        colors = sns.color_palette("tab10", num_categories)
    elif num_categories <= 20:
        colors = sns.color_palette("tab20", num_categories)
    else:
        # For more categories, a larger palette or a sequential one might be better
        colors = sns.color_palette("viridis", num_categories)

# Pie Chart (Categorical Features with Few Categories)

def create_donut_chart_processed(df, column, title="Donut Chart"):
    """
    Creates a donut chart for a specified categorical column in a DataFrame.

    Args:
        df (pd.DataFrame): The dataframe containing the data.
        column (str): The column name for the donut chart.
        title (str): The title for the chart.
    """
    # --- Input Validation ---
    if not isinstance(df, pd.DataFrame):
        print("Error: Input 'df' must be a pandas DataFrame.")
        return
    if column not in df.columns:
        print(f"Error: Column '{column}' not found in DataFrame.")
        return

    # --- Data Preparation ---
    try:
        data_counts = df[column].dropna().value_counts()
        if data_counts.empty:
            print(f"Error: Column '{column}' has no data after dropping NaNs.")
            return
        # Calculate percentages
        data_percentages = data_counts / data_counts.sum() * 100
        # Combine counts and percentages for labels
        labels = [f'{idx} ({count} - {pct:.1f}%)' for idx, count, pct in zip(data_counts.index, data_counts.values, data_percentages.values)]
    except Exception as e:
        print(f"Error during data preparation for column '{column}': {e}")
        return

    # --- Color Palette ---
    # Use a colorful palette based on the number of categories
    num_categories = len(data_counts)
    if num_categories <= 10:
        colors = sns.color_palette("tab10", num_categories)
    elif num_categories <= 20:
        colors = sns.color_palette("tab20", num_categories)
    else:
        # For more categories, a larger palette or a sequential one might be better
        colors = sns.color_palette("viridis", num_categories)

    # --- Plotting ---
    # Create a new figure and axes for each plot
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.set_title(title, fontsize=16, fontweight="bold")

    # Create the pie chart
    wedges, texts, autotexts = ax.pie(
        data_counts.values,
        colors=colors,
        startangle=90,
        counterclock=False,
        wedgeprops=dict(width=0.4, edgecolor="white", linewidth=2), # Create the donut hole
        autopct='', # Disable default autopct
        pctdistance=0.85 # Position for potential percentages if needed inside
    )

    # Add text labels with counts and percentages outside the donut
    bbox_props = dict(boxstyle="square,pad=0.3", fc="w", ec="k", lw=0.7)
    kw = dict(arrowprops=dict(arrowstyle="-"), bbox=bbox_props, zorder=0, va="center")

    for i, p in enumerate(wedges):
        ang = (p.theta2 - p.theta1) / 2. + p.theta1
        y = np.sin(np.deg2rad(ang))
        x = np.cos(np.deg2rad(ang))
        horizontalalignment = {-1: "right", 1: "left"}[int(np.sign(x))]
        connectionstyle = "angle,angleA=0,angleB={}".format(ang)
        kw["arrowprops"].update({"connectionstyle": connectionstyle})
        ax.annotate(labels[i], xy=(x, y), xytext=(1.35*np.sign(x), 1.4*y),
                    horizontalalignment=horizontalalignment, **kw)


    # Add a circle in the center to complete the donut shape
    centre_circle = plt.Circle((0,0),0.25,fc='white')
    fig = plt.gcf()
    fig.gca().add_artist(centre_circle)

    ax.axis("equal") # Equal aspect ratio ensures that pie is drawn as a circle.
    plt.tight_layout()
    plt.show() # Display the plot

# --- Example Usage (Preprocessed Data) ---
print("--- Donut Charts for Key Categorical Features (Preprocessed Data) --- \n")
# Example usage of the function for a few columns
key_cat_donut_proc = ["mszoning", "centralair", "paveddrive"] # Choose columns with few categories
# Ensure column names exist in the DataFrame, using .columns property directly
key_cat_donut_proc = [col for col in key_cat_donut_proc if col in df_processed.columns]


if df_processed is not None:
    for col in key_cat_donut_proc:
        # Check if the column is suitable for a pie/donut chart (not too many unique values)
        if df_processed[col].nunique() <= 20: # Limit to columns with 20 or fewer unique values
            create_donut_chart_processed(df_processed, col, title=f"Distribution of {col} (Preprocessed)")
            print("\n" + "="*80 + "\n") # Add a separator (e.g., newlines and line) after each plot
        else:
            print(f"Skipping donut chart for '{col}': Too many unique categories ({df_processed[col].nunique()}).")
else:
    print("Preprocessed data not loaded. Cannot create donut charts.")

print(f"--- Scatter Plots (Numerical vs. {target_col_proc} - Preprocessed Data) ---")
key_num_scatter_proc = ["grlivarea", "totalbsmtsf", "1stflrsf", "garagearea", "yearbuilt"]
# Add engineered features if they exist
engineered_scatter = ["totalsf", "houseage"]
key_num_scatter_proc.extend([col for col in engineered_scatter if col in df_processed.columns.str.lower()])
key_num_scatter_proc = [col for col in key_num_scatter_proc if col is not None and col in df_processed.columns] # Ensure column exists
key_num_scatter_proc = list(dict.fromkeys(key_num_scatter_proc)) # Remove duplicate

# Get a colorful palette with enough colors for all scatter plots
colorful_palette = sns.color_palette("viridis", len(key_num_scatter_proc))

for i, col in enumerate(key_num_scatter_proc):
    if col in df_processed.columns and target_col_proc in df_processed.columns:
        plt.figure(figsize=(8, 5))
        # Drop NA for the pair being plotted
        plot_data = df_processed.dropna(subset=[col, target_col_proc])
        sns.scatterplot(x=col, y=target_col_proc, data=plot_data, alpha=0.6, color=colorful_palette[i]) # Use color from palette
        plt.title(f"{col} vs. {target_col_proc} (Preprocessed)")
        plt.show()
        # Print a dotted line after each plot
        print("-" * 50)

print(f"--- Box Plots (Categorical vs. {target_col_proc} - Preprocessed Data) ---")
key_cat_box_proc = ["overallqual", "neighborhood", "centralair", "garagecars"]
key_cat_box_proc = [col for col in key_cat_box_proc if col in df_processed.columns and (df_processed[col].dtype == "object" or pd.api.types.is_integer_dtype(df_processed[col]))] # Include integers

for col in key_cat_box_proc:
        plt.figure(figsize=(12, 6))
        plot_data = df_processed.dropna(subset=[col, target_col_proc])

        if plot_data.empty:
             print(f"Warning: No data available for plotting {col} vs {target_col_proc} after dropping NaNs.")
             plt.close() # Close the empty figure
             continue

        # Order categories by median target value for clarity
        try:
           order = plot_data.groupby(col)[target_col_proc].median().sort_values().index
        except TypeError:
           order = plot_data[col].value_counts().index # Fallback

        # Get a colorful palette suitable for the number of categories
        num_categories = len(plot_data[col].unique())
        if num_categories <= 10:
             category_palette = sns.color_palette("tab10", num_categories)
        elif num_categories <= 20:
             category_palette = sns.color_palette("tab20", num_categories)
        else:
             category_palette = sns.color_palette("viridis", num_categories)

        sns.boxplot(x=col, y=target_col_proc, data=plot_data, order=order, palette=category_palette)
        plt.title(f"{col} vs. {target_col_proc} (Preprocessed)")
        plt.xlabel(col)
        plt.ylabel(target_col_proc)

        if plot_data[col].nunique() > 10: # Rotate labels if many categories
             plt.xticks(rotation=60, ha="right")

        plt.tight_layout()
        plt.show()

        # Add a dotted line after each plot for separation
        print("-" * 50) # Print a dotted line

if df_processed is not None and target_col_proc is not None:
    print(f"--- Violin Plots (Categorical vs. {target_col_proc} - Preprocessed Data) ---")
    key_cat_violin_proc = ["overallqual", "centralair", "paveddrive"]
    key_cat_violin_proc = [col for col in key_cat_violin_proc if col in df_processed.columns and (df_processed[col].dtype == "object" or pd.api.types.is_integer_dtype(df_processed[col]))]

    for col in key_cat_violin_proc:
        plt.figure(figsize=(12, 6))
        plot_data = df_processed.dropna(subset=[col, target_col_proc])

        if plot_data.empty:
             print(f"Warning: No data available for plotting {col} vs {target_col_proc} after dropping NaNs.")
             plt.close() # Close the empty figure
             continue

        try:
           order = plot_data.groupby(col)[target_col_proc].median().sort_values().index
        except TypeError:
           order = plot_data[col].value_counts().index # Fallback

        # Use a colorful palette based on the number of unique categories
        num_categories = len(plot_data[col].unique())
        if num_categories <= 10:
            category_palette = sns.color_palette("tab10", num_categories)
        elif num_categories <= 20:
            category_palette = sns.color_palette("tab20", num_categories)
        else:
            # For more categories, a larger palette or a sequential one might be better
            category_palette = sns.color_palette("viridis", num_categories)

        sns.violinplot(x=col, y=target_col_proc, data=plot_data, order=order, inner="quartile", palette=category_palette) # Use the colorful palette
        plt.title(f"{col} vs. {target_col_proc} (Preprocessed - Violin Plot)")
        plt.tight_layout()
        plt.show()

        # Add a dotted line after each graph for visual separation
        print("-" * 50)

print(f"--- Bar Plots (Average {target_col_proc} by Category - Preprocessed Data) ---")
key_cat_bar_proc = ["housestyle", "mszoning", "foundation"]
key_cat_bar_proc = [col for col in key_cat_bar_proc if col in df_processed.columns and df_processed[col].dtype == "object"]

for col in key_cat_bar_proc:
        plt.figure(figsize=(10, 5))
        plot_data = df_processed.dropna(subset=[col, target_col_proc])
        try:
           order = plot_data.groupby(col)[target_col_proc].mean().sort_values(ascending=False).index
        except TypeError:
           order = plot_data[col].value_counts().index # Fallback

        # Use a colorful palette based on the number of unique categories
        num_categories = len(order)
        if num_categories <= 10:
            category_palette = sns.color_palette("tab10", num_categories)
        elif num_categories <= 20:
            category_palette = sns.color_palette("tab20", num_categories)
        else:
            # For more categories, a larger palette or a sequential one might be better
            category_palette = sns.color_palette("viridis", num_categories)

        sns.barplot(x=col, y=target_col_proc, data=plot_data, order=order, estimator=np.mean, errorbar=None, palette=category_palette)
        plt.title(f"Average {target_col_proc} by {col} (Preprocessed)")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()
        plt.show()

        # Add a dotted line to indicate the end of the plot
        print("-" * 50) # Print a dotted line after each graph

if df_processed is not None and target_col_proc is not None:
    print(f"--- Joint Plots (Key Numerical vs. {target_col_proc} - Preprocessed Data) ---")
    key_num_joint_proc = ["grlivarea", "totalbsmtsf"]
    key_num_joint_proc = [col for col in key_num_joint_proc if col in df_processed.columns]

    for col in key_num_joint_proc:
        plot_data = df_processed.dropna(subset=[col, target_col_proc])

        # Use a colorful palette for the joint plot
        palette = sns.color_palette("viridis", 2) # Get two colors from the palette
        joint_color = palette[0] # Color for the scatter points
        marginal_color = palette[1] # Color for the marginal distributions

        g = sns.jointplot(
            x=col, y=target_col_proc, data=plot_data, kind="reg",
            color=joint_color, # Set the main color for the plot
            scatter_kws={'alpha': 0.6, 's': 20}, # Customize scatter points
            marginal_kws={'color': marginal_color, 'edgecolor': 'black'}, # Customize marginal plots
            line_kws={'color': 'red'} # Customize the regression line color
        )
        g.fig.suptitle(f"Joint Distribution of {col} and {target_col_proc} (Preprocessed)", y=1.02)
        plt.show()

        # Add a dotted line separator after each plot
        print("-" * 50) # Print a dotted line of 50 dashes

print("--- Correlation Heatmap (Preprocessed Data) ---\n")

# Select only numerical columns
numerical_cols_proc = df_processed.select_dtypes(include=np.number).columns
correlation_matrix_proc = df_processed[numerical_cols_proc].corr()
plt.figure(figsize=(20, 16)) # May need larger figure for more columns
sns.heatmap(correlation_matrix_proc, cmap="coolwarm", annot=False, fmt=".1f")
plt.title("Correlation Matrix of Numerical Features (Preprocessed Data)")
plt.show()

# Show top correlations with the target variable
if target_col_proc is not None and target_col_proc in correlation_matrix_proc.columns:
        print(f"Top Absolute Correlations with {target_col_proc} (Preprocessed):")
        top_corr_proc = correlation_matrix_proc[target_col_proc].abs().sort_values(ascending=False)
        display(top_corr_proc.head(20)) # Show more correlations
else:
  print(f"Target column {target_col_proc} not found or is not numerical for correlation analysis.")

print("--- Pair Plot (Subset of Key Features - Preprocessed Data) ---")
# Select a small subset of key numerical features + target
key_features_pair_proc = [target_col_proc, "overallqual", "grlivarea", "garagearea", "totalbsmtsf"]
# Add engineered features if they exist and are numerical
engineered_pair = ["totalsf", "houseage"]
# Find the actual column names that match the lowercase engineered feature names
actual_engineered_cols = []
for eng_col_lower in engineered_pair:
    # Find the first column in df_processed whose lowercase name matches eng_col_lower
    matching_col = next((col for col in df_processed.columns if col.lower() == eng_col_lower), None)
    # If a match is found and it's numeric, add the actual column name
    if matching_col is not None and pd.api.types.is_numeric_dtype(df_processed[matching_col]):
         actual_engineered_cols.append(matching_col)
key_features_pair_proc.extend(actual_engineered_cols)
key_features_pair_proc = [col for col in key_features_pair_proc if col is not None and col in df_processed.columns]
key_features_pair_proc = list(dict.fromkeys(key_features_pair_proc)) # Remove duplicates
if len(key_features_pair_proc) > 1:
    plot_data = df_processed[key_features_pair_proc].dropna()
    # Create a colorful palette for the scatter plots and KDE plots
    num_features = len(key_features_pair_proc)
    colorful_palette = sns.color_palette("viridis", num_features) # Choose a suitable palette
    g = sns.pairplot(plot_data, diag_kind="kde", palette=colorful_palette) # Use kde for diagonal plots and apply palette
    # Improve clarity for the X-axis labels on the bottom row
    for ax in g.axes[-1, :]: # Iterate through the axes in the bottom row
        plt.setp(ax.get_xticklabels(), rotation=45, ha="right") # Rotate labels for better visibility
    g.fig.suptitle("Pairwise Relationships of Key Features (Preprocessed)", y=1.02)
    plt.show()
    # Add a dotted line separator after the pair plot
    plt.figure(figsize=(15, 0.1))
    plt.plot([0, 1], [0.5, 0.5], 'k:', lw=2) # Dotted line from x=0 to x=1 at y=0.5
    plt.axis('off') # Hide axes
    plt.show()
else:
  print("Not enough key numerical features found for pair plot.")

print(f"--- FacetGrid Example ({target_col_proc} Distribution by OverallQual - Preprocessed Data) ---")
if "overallqual" in df_processed.columns.str.lower():
        qual_col = [col for col in df_processed.columns if col.lower() == "overallqual"][0]
        plot_data = df_processed.dropna(subset=[qual_col, target_col_proc])
        # Create a colorful palette based on the number of unique 'OverallQual' values
        num_qual_categories = plot_data[qual_col].nunique()
        category_palette = sns.color_palette('viridis', n_colors=num_qual_categories)

        g = sns.FacetGrid(plot_data, col=qual_col, col_wrap=5, palette=category_palette, height=3, aspect=1) # Add palette and adjust size
        g.map(sns.histplot, target_col_proc, kde=False, bins=10)

        # You might need to manually set colors if map doesn't use the palette correctly,
        # but FacetGrid's palette argument should handle it for 'col'.
        # If not, uncomment and adapt the color setting loop from the previous FacetGrid example.

        g.fig.suptitle(f"{target_col_proc} Distribution Faceted by {qual_col} (Preprocessed)", y=1.03)
        g.set_titles(f"{qual_col} {{col_name}}")
        plt.tight_layout()
        plt.show()
else:
  print("OverallQual column not found for FacetGrid.")

print("--- Subplots Example (Multiple Numerical Distributions - Preprocessed Data) ---")
key_num_subplot_proc = ["lotarea", "totalbsmtsf", "1stflrsf", "grlivarea"]
# Add engineered features
engineered_subplot = ["totalsf", "houseage"]
key_num_subplot_proc.extend([col for col in engineered_subplot if col in df_processed.columns.str.lower()])
key_num_subplot_proc = [col for col in key_num_subplot_proc if col in df_processed.columns]
key_num_subplot_proc = list(dict.fromkeys(key_num_subplot_proc)) # Remove duplicate
# Adjust grid size based on number of features
n_features = len(key_num_subplot_proc)
n_cols = 3
n_rows = (n_features + n_cols - 1) // n_cols
if n_features > 0:
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols * 5, n_rows * 4))
        axes = axes.flatten() # Flatten to 1D array for easy iteration
        fig.suptitle("Distributions using Subplots (Preprocessed)", fontsize=16)

        for i, col in enumerate(key_num_subplot_proc):
            sns.histplot(df_processed[col].dropna(), ax=axes[i], kde=True,palette='viridis')
            axes[i].set_title(col)

        # Hide unused subplots
        for j in range(i + 1, len(axes)):
            axes[j].axis("off")

        plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout
        plt.show()
else:
  print("Could not find specified columns for subplot example.")

# This section was partially covered in the histogram and scatter plots above.
# We can specifically look at correlations of engineered features.
if df_processed is not None and target_col_proc is not None:
    print(f"--- Correlation of Engineered Features with {target_col_proc} ---")
    engineered_features_corr = ["totalsf", "totalbathrooms", "totalporchsf", "isnew", "houseage"] # Example names
    engineered_features_found = [col for col in engineered_features_corr if col in df_processed.columns.str.lower()]
    engineered_features_found = [col for col in engineered_features_found if col in correlation_matrix_proc.columns] # Ensure they are numerical

    if engineered_features_found and target_col_proc in correlation_matrix_proc.columns:
        engineered_corr = correlation_matrix_proc.loc[engineered_features_found, target_col_proc]
        display(engineered_corr.sort_values(ascending=False))
    else:
        print("No common engineered features found or target not available for correlation.")















